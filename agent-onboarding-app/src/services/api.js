import axios from 'axios';
import { API_ENDPOINTS, DROPDOWN_OPTIONS } from '../utils/constants';

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
apiClient.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('authToken');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Mock data for development
const mockClients = JSON.parse(localStorage.getItem('mockClients') || '[]');
const mockOnboardingData = JSON.parse(localStorage.getItem('mockOnboardingData') || '{}');

// Client API functions
export const clientAPI = {
  // Get all clients
  getAll: async () => {
    try {
      // In development, return mock data
      if (import.meta.env.DEV) {
        return { data: mockClients };
      }
      const response = await apiClient.get(API_ENDPOINTS.clients);
      return response.data;
    } catch (error) {
      console.error('Error fetching clients:', error);
      throw error;
    }
  },

  // Get client by ID
  getById: async (id) => {
    try {
      if (import.meta.env.DEV) {
        const client = mockClients.find(c => c.id === id);
        return { data: client };
      }
      const response = await apiClient.get(`${API_ENDPOINTS.clients}/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching client:', error);
      throw error;
    }
  },

  // Create new client
  create: async (clientData) => {
    try {
      if (import.meta.env.DEV) {
        const newClient = {
          id: Date.now().toString(),
          ...clientData,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        mockClients.push(newClient);
        localStorage.setItem('mockClients', JSON.stringify(mockClients));
        return { data: newClient };
      }
      const response = await apiClient.post(API_ENDPOINTS.clients, clientData);
      return response.data;
    } catch (error) {
      console.error('Error creating client:', error);
      throw error;
    }
  },

  // Update client
  update: async (id, clientData) => {
    try {
      if (import.meta.env.DEV) {
        const index = mockClients.findIndex(c => c.id === id);
        if (index !== -1) {
          mockClients[index] = {
            ...mockClients[index],
            ...clientData,
            updatedAt: new Date().toISOString(),
          };
          localStorage.setItem('mockClients', JSON.stringify(mockClients));
          return { data: mockClients[index] };
        }
        throw new Error('Client not found');
      }
      const response = await apiClient.put(`${API_ENDPOINTS.clients}/${id}`, clientData);
      return response.data;
    } catch (error) {
      console.error('Error updating client:', error);
      throw error;
    }
  },

  // Delete client
  delete: async (id) => {
    try {
      if (import.meta.env.DEV) {
        const index = mockClients.findIndex(c => c.id === id);
        if (index !== -1) {
          mockClients.splice(index, 1);
          localStorage.setItem('mockClients', JSON.stringify(mockClients));
          return { success: true };
        }
        throw new Error('Client not found');
      }
      await apiClient.delete(`${API_ENDPOINTS.clients}/${id}`);
      return { success: true };
    } catch (error) {
      console.error('Error deleting client:', error);
      throw error;
    }
  },
};

// Dropdown API functions
export const dropdownAPI = {
  // Get all dropdown options
  getAll: async () => {
    try {
      if (import.meta.env.DEV) {
        return { data: DROPDOWN_OPTIONS };
      }
      const response = await apiClient.get(API_ENDPOINTS.dropdowns);
      return response.data;
    } catch (error) {
      console.error('Error fetching dropdown options:', error);
      // Fallback to constants if API fails
      return { data: DROPDOWN_OPTIONS };
    }
  },

  // Get specific dropdown options
  getByType: async (type) => {
    try {
      if (import.meta.env.DEV) {
        return { data: DROPDOWN_OPTIONS[type] || [] };
      }
      const response = await apiClient.get(`${API_ENDPOINTS.dropdowns}/${type}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching ${type} options:`, error);
      return { data: DROPDOWN_OPTIONS[type] || [] };
    }
  },
};

// Validation API functions
export const validationAPI = {
  // Check if value is unique
  checkUnique: async (field, value) => {
    try {
      if (import.meta.env.DEV) {
        // Mock unique validation
        const existingValues = {
          nicNo: ['123456789V', '987654321V', '111222333V'],
        };
        const isUnique = !existingValues[field]?.includes(value);
        return { data: { isUnique } };
      }
      const response = await apiClient.post(API_ENDPOINTS.validation.unique, {
        field,
        value,
      });
      return response.data;
    } catch (error) {
      console.error('Error checking uniqueness:', error);
      throw error;
    }
  },
};

// Report API functions
export const reportAPI = {
  // Generate GS Report
  generateGS: async (clientId) => {
    try {
      if (import.meta.env.DEV) {
        // Mock report generation
        const client = mockClients.find(c => c.id === clientId);
        if (!client) {
          throw new Error('Client not found');
        }

        // Check if onboarding is complete (mock check)
        const requiredSections = ['personalDetails', 'contactInfo', 'documents'];
        const onboardingData = mockOnboardingData[clientId] || {};
        const isComplete = requiredSections.every(section => onboardingData[section]);

        if (!isComplete) {
          throw new Error('Unable to generate GS Report: Incomplete onboarding data.');
        }

        return {
          data: {
            reportUrl: '/mock-gs-report.pdf',
            message: 'GS Report generated successfully.'
          }
        };
      }
      const response = await apiClient.post(`${API_ENDPOINTS.reports.gs}/${clientId}`);
      return response.data;
    } catch (error) {
      console.error('Error generating GS report:', error);
      throw error;
    }
  },

  // Generate HOB Report
  generateHOB: async (clientId) => {
    try {
      if (import.meta.env.DEV) {
        // Mock report generation
        const client = mockClients.find(c => c.id === clientId);
        if (!client) {
          throw new Error('Client not found');
        }

        // Check if business history exists (mock check)
        const hasBusinessHistory = Math.random() > 0.5; // Random for demo

        if (!hasBusinessHistory) {
          throw new Error('HOB Report generation failed: No historical business data found.');
        }

        return {
          data: {
            reportUrl: '/mock-hob-report.pdf',
            message: 'HOB Report generated successfully.'
          }
        };
      }
      const response = await apiClient.post(`${API_ENDPOINTS.reports.hob}/${clientId}`);
      return response.data;
    } catch (error) {
      console.error('Error generating HOB report:', error);
      throw error;
    }
  },
};

// Export default API object
export default {
  client: clientAPI,
  dropdown: dropdownAPI,
  validation: validationAPI,
  report: reportAPI,
};
