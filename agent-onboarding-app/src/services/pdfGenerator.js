import jsPDF from 'jspdf';
import { formatDate } from '../utils/helpers';

/**
 * Generate GS Report PDF
 * @param {object} clientData - Client data
 * @param {object} onboardingData - Onboarding data
 * @returns {Promise<Blob>} - PDF blob
 */
export const generateGSReport = async (clientData, onboardingData = {}) => {
  const pdf = new jsPDF();
  const pageWidth = pdf.internal.pageSize.width;
  const margin = 20;
  let yPosition = 30;

  // Header
  pdf.setFontSize(20);
  pdf.setFont('helvetica', 'bold');
  pdf.text('GS Report - Agent Onboarding Summary', margin, yPosition);
  
  yPosition += 20;
  pdf.setFontSize(12);
  pdf.setFont('helvetica', 'normal');
  pdf.text(`Generated on: ${formatDate(new Date())}`, margin, yPosition);
  
  yPosition += 20;

  // Personal Details Section
  pdf.setFontSize(16);
  pdf.setFont('helvetica', 'bold');
  pdf.text('Personal Details', margin, yPosition);
  yPosition += 15;

  pdf.setFontSize(11);
  pdf.setFont('helvetica', 'normal');
  
  const personalDetails = [
    ['Name:', `${clientData.firstName || ''} ${clientData.lastName || ''}`.trim() || 'N/A'],
    ['Designation:', clientData.designation || 'N/A'],
    ['NIC No:', clientData.nicNo || 'N/A'],
    ['Date of Birth:', clientData.dateOfBirth ? formatDate(clientData.dateOfBirth) : 'N/A'],
    ['Nationality:', clientData.nationality || 'N/A'],
    ['Civil Status:', clientData.civilStatus || 'N/A'],
    ['Having Children:', clientData.havingChildren || 'N/A'],
    ['Takaful Agent:', clientData.takafulAgent || 'N/A'],
  ];

  personalDetails.forEach(([label, value]) => {
    pdf.text(label, margin, yPosition);
    pdf.text(value, margin + 60, yPosition);
    yPosition += 12;
  });

  yPosition += 10;

  // Contact Info Section
  pdf.setFontSize(16);
  pdf.setFont('helvetica', 'bold');
  pdf.text('Contact Information', margin, yPosition);
  yPosition += 15;

  pdf.setFontSize(11);
  pdf.setFont('helvetica', 'normal');
  
  const contactInfo = [
    ['Preferred Language:', clientData.preferredLanguage || 'N/A'],
    ['Passport No:', clientData.passportNo || 'N/A'],
  ];

  contactInfo.forEach(([label, value]) => {
    pdf.text(label, margin, yPosition);
    pdf.text(value, margin + 60, yPosition);
    yPosition += 12;
  });

  yPosition += 10;

  // Onboarding Status Section
  pdf.setFontSize(16);
  pdf.setFont('helvetica', 'bold');
  pdf.text('Onboarding Status', margin, yPosition);
  yPosition += 15;

  pdf.setFontSize(11);
  pdf.setFont('helvetica', 'normal');
  
  const requiredFields = ['firstName', 'lastName', 'designation', 'civilStatus', 'nationality', 'dateOfBirth', 'nicNo', 'takafulAgent', 'havingChildren'];
  const completedFields = requiredFields.filter(field => clientData[field]);
  const completionPercentage = Math.round((completedFields.length / requiredFields.length) * 100);

  pdf.text(`Completion Status: ${completionPercentage}%`, margin, yPosition);
  yPosition += 12;
  pdf.text(`Completed Fields: ${completedFields.length}/${requiredFields.length}`, margin, yPosition);
  yPosition += 12;

  // Footer
  yPosition = pdf.internal.pageSize.height - 30;
  pdf.setFontSize(10);
  pdf.setFont('helvetica', 'italic');
  pdf.text('This is a system-generated report.', margin, yPosition);

  return pdf.output('blob');
};

/**
 * Generate HOB Report PDF
 * @param {object} clientData - Client data
 * @param {object} businessData - Business history data
 * @returns {Promise<Blob>} - PDF blob
 */
export const generateHOBReport = async (clientData, businessData = {}) => {
  const pdf = new jsPDF();
  const pageWidth = pdf.internal.pageSize.width;
  const margin = 20;
  let yPosition = 30;

  // Header
  pdf.setFontSize(20);
  pdf.setFont('helvetica', 'bold');
  pdf.text('HOB Report - Agent Business History', margin, yPosition);
  
  yPosition += 20;
  pdf.setFontSize(12);
  pdf.setFont('helvetica', 'normal');
  pdf.text(`Generated on: ${formatDate(new Date())}`, margin, yPosition);
  
  yPosition += 20;

  // Agent Information
  pdf.setFontSize(16);
  pdf.setFont('helvetica', 'bold');
  pdf.text('Agent Information', margin, yPosition);
  yPosition += 15;

  pdf.setFontSize(11);
  pdf.setFont('helvetica', 'normal');
  
  const agentInfo = [
    ['Name:', `${clientData.firstName || ''} ${clientData.lastName || ''}`.trim() || 'N/A'],
    ['NIC No:', clientData.nicNo || 'N/A'],
    ['Agent Type:', clientData.takafulAgent || 'N/A'],
  ];

  agentInfo.forEach(([label, value]) => {
    pdf.text(label, margin, yPosition);
    pdf.text(value, margin + 60, yPosition);
    yPosition += 12;
  });

  yPosition += 10;

  // Previous Roles Section
  pdf.setFontSize(16);
  pdf.setFont('helvetica', 'bold');
  pdf.text('Previous Roles', margin, yPosition);
  yPosition += 15;

  pdf.setFontSize(11);
  pdf.setFont('helvetica', 'normal');
  
  // Mock data for demonstration
  const previousRoles = businessData.previousRoles || [
    { role: 'Junior Agent', period: '2020-2022', performance: 'Good' },
    { role: 'Senior Agent', period: '2022-2024', performance: 'Excellent' },
  ];

  if (previousRoles.length === 0) {
    pdf.text('No previous role history available.', margin, yPosition);
    yPosition += 12;
  } else {
    previousRoles.forEach((role, index) => {
      pdf.text(`${index + 1}. ${role.role} (${role.period})`, margin, yPosition);
      yPosition += 10;
      pdf.text(`   Performance: ${role.performance}`, margin, yPosition);
      yPosition += 15;
    });
  }

  yPosition += 10;

  // Performance Metrics Section
  pdf.setFontSize(16);
  pdf.setFont('helvetica', 'bold');
  pdf.text('Performance Metrics', margin, yPosition);
  yPosition += 15;

  pdf.setFontSize(11);
  pdf.setFont('helvetica', 'normal');
  
  const performanceMetrics = businessData.performanceMetrics || {
    totalSales: 'N/A',
    clientRetention: 'N/A',
    averageRating: 'N/A',
  };

  const metrics = [
    ['Total Sales:', performanceMetrics.totalSales],
    ['Client Retention Rate:', performanceMetrics.clientRetention],
    ['Average Rating:', performanceMetrics.averageRating],
  ];

  metrics.forEach(([label, value]) => {
    pdf.text(label, margin, yPosition);
    pdf.text(value.toString(), margin + 80, yPosition);
    yPosition += 12;
  });

  yPosition += 10;

  // Client Feedback Section
  pdf.setFontSize(16);
  pdf.setFont('helvetica', 'bold');
  pdf.text('Client Feedback', margin, yPosition);
  yPosition += 15;

  pdf.setFontSize(11);
  pdf.setFont('helvetica', 'normal');
  
  const clientFeedback = businessData.clientFeedback || [
    'Professional and knowledgeable service.',
    'Always responsive to client needs.',
    'Excellent communication skills.',
  ];

  if (clientFeedback.length === 0) {
    pdf.text('No client feedback available.', margin, yPosition);
  } else {
    clientFeedback.forEach((feedback, index) => {
      pdf.text(`${index + 1}. ${feedback}`, margin, yPosition);
      yPosition += 12;
    });
  }

  // Footer
  yPosition = pdf.internal.pageSize.height - 30;
  pdf.setFontSize(10);
  pdf.setFont('helvetica', 'italic');
  pdf.text('This is a system-generated report.', margin, yPosition);

  return pdf.output('blob');
};

/**
 * Download PDF blob as file
 * @param {Blob} pdfBlob - PDF blob
 * @param {string} filename - File name
 */
export const downloadPDF = (pdfBlob, filename) => {
  const url = URL.createObjectURL(pdfBlob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

export default {
  generateGSReport,
  generateHOBReport,
  downloadPDF,
};
