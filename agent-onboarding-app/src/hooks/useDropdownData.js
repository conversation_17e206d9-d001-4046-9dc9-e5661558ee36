import { useState, useEffect } from 'react';
import api from '../services/api';
import { DROPDOWN_OPTIONS } from '../utils/constants';

/**
 * Custom hook for managing dropdown data
 * @param {string|null} type - Specific dropdown type to fetch, or null for all
 * @returns {object} - { data, loading, error, refetch }
 */
export const useDropdownData = (type = null) => {
  const [data, setData] = useState(type ? [] : DROPDOWN_OPTIONS);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      let response;
      if (type) {
        response = await api.dropdown.getByType(type);
        setData(response.data);
      } else {
        response = await api.dropdown.getAll();
        setData(response.data);
      }
    } catch (err) {
      console.error('Error fetching dropdown data:', err);
      setError(err.message);
      // Fallback to default options
      if (type) {
        setData(DROPDOWN_OPTIONS[type] || []);
      } else {
        setData(DROPDOWN_OPTIONS);
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [type]);

  return {
    data,
    loading,
    error,
    refetch: fetchData,
  };
};

export default useDropdownData;
