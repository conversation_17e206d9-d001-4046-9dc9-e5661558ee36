import { useState, useCallback } from 'react';
import { validateField, validateForm, areRequiredFieldsFilled } from '../services/validation';

/**
 * Custom hook for form validation
 * @param {object} initialValues - Initial form values
 * @returns {object} - Validation utilities and state
 */
export const useFormValidation = (initialValues = {}) => {
  const [fieldErrors, setFieldErrors] = useState({});
  const [isValidating, setIsValidating] = useState(false);

  // Validate a single field
  const validateSingleField = useCallback(async (fieldName, value) => {
    try {
      setIsValidating(true);
      const result = await validateField(fieldName, value);
      
      setFieldErrors(prev => ({
        ...prev,
        [fieldName]: result.isValid ? null : result.error,
      }));
      
      return result;
    } catch (error) {
      console.error('Field validation error:', error);
      return { isValid: false, error: 'Validation error' };
    } finally {
      setIsValidating(false);
    }
  }, []);

  // Validate entire form
  const validateEntireForm = useCallback(async (formData) => {
    try {
      setIsValidating(true);
      const result = await validateForm(formData);
      
      setFieldErrors(result.errors || {});
      
      return result;
    } catch (error) {
      console.error('Form validation error:', error);
      return { isValid: false, errors: {} };
    } finally {
      setIsValidating(false);
    }
  }, []);

  // Check if required fields are filled
  const checkRequiredFields = useCallback((formData) => {
    return areRequiredFieldsFilled(formData);
  }, []);

  // Clear field error
  const clearFieldError = useCallback((fieldName) => {
    setFieldErrors(prev => ({
      ...prev,
      [fieldName]: null,
    }));
  }, []);

  // Clear all errors
  const clearAllErrors = useCallback(() => {
    setFieldErrors({});
  }, []);

  // Get error for specific field
  const getFieldError = useCallback((fieldName) => {
    return fieldErrors[fieldName];
  }, [fieldErrors]);

  // Check if field has error
  const hasFieldError = useCallback((fieldName) => {
    return Boolean(fieldErrors[fieldName]);
  }, [fieldErrors]);

  // Check if form has any errors
  const hasErrors = useCallback(() => {
    return Object.values(fieldErrors).some(error => Boolean(error));
  }, [fieldErrors]);

  return {
    fieldErrors,
    isValidating,
    validateSingleField,
    validateEntireForm,
    checkRequiredFields,
    clearFieldError,
    clearAllErrors,
    getFieldError,
    hasFieldError,
    hasErrors,
  };
};

export default useFormValidation;
