import React from 'react';
import {
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  FormHelperText,
} from '@mui/material';
import { Controller } from 'react-hook-form';

const FormRadioGroup = ({
  name,
  control,
  label,
  required = false,
  options = [],
  row = true,
  helperText,
  disabled = false,
  sx = {},
  ...props
}) => {
  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState: { error } }) => (
        <FormControl 
          component="fieldset" 
          fullWidth 
          sx={{ mb: 2, ...sx }} 
          error={!!error}
        >
          <FormLabel 
            component="legend" 
            required={required}
            sx={{ 
              mb: 1,
              color: error ? 'error.main' : 'text.primary',
              '&.Mui-focused': {
                color: error ? 'error.main' : 'primary.main',
              },
            }}
          >
            {label}
          </FormLabel>
          <RadioGroup
            value={field.value || ''}
            onChange={field.onChange}
            onBlur={field.onBlur}
            name={field.name}
            row={row}
            disabled={disabled}
            sx={{
              '& .MuiFormControlLabel-root': {
                mr: row ? 3 : 0,
                mb: row ? 0 : 1,
              },
            }}
            {...props}
          >
            {options.map((option) => (
              <FormControlLabel
                key={option.value}
                value={option.value}
                control={
                  <Radio 
                    sx={{
                      color: error ? 'error.main' : 'primary.main',
                      '&.Mui-checked': {
                        color: error ? 'error.main' : 'primary.main',
                      },
                    }}
                  />
                }
                label={option.label}
                disabled={disabled}
              />
            ))}
          </RadioGroup>
          {(error?.message || helperText) && (
            <FormHelperText>{error?.message || helperText}</FormHelperText>
          )}
        </FormControl>
      )}
    />
  );
};

export default FormRadioGroup;
