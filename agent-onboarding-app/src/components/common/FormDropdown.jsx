import React from 'react';
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  CircularProgress,
  Box,
} from '@mui/material';
import { Controller } from 'react-hook-form';

const FormDropdown = ({
  name,
  control,
  label,
  required = false,
  options = [],
  loading = false,
  placeholder = 'Select an option',
  helperText,
  disabled = false,
  sx = {},
  ...props
}) => {
  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState: { error } }) => (
        <FormControl fullWidth sx={{ mb: 2, ...sx }} error={!!error}>
          <InputLabel required={required}>{label}</InputLabel>
          <Select
            value={field.value || ''}
            onChange={field.onChange}
            onBlur={field.onBlur}
            name={field.name}
            label={label}
            disabled={disabled || loading}
            displayEmpty
            sx={{
              '& .MuiOutlinedInput-notchedOutline': {
                borderColor: error ? 'error.main' : undefined,
              },
              '&:hover .MuiOutlinedInput-notchedOutline': {
                borderColor: error ? 'error.main' : 'primary.main',
              },
              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                borderColor: error ? 'error.main' : 'primary.main',
              },
            }}
            {...props}
          >
            {loading ? (
              <MenuItem disabled>
                <Box display="flex" alignItems="center" gap={1}>
                  <CircularProgress size={16} />
                  Loading...
                </Box>
              </MenuItem>
            ) : (
              <>
                {!required && (
                  <MenuItem value="">
                    <em>{placeholder}</em>
                  </MenuItem>
                )}
                {options.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </>
            )}
          </Select>
          {(error?.message || helperText) && (
            <FormHelperText>{error?.message || helperText}</FormHelperText>
          )}
        </FormControl>
      )}
    />
  );
};

export default FormDropdown;
