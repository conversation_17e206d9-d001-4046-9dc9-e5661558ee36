import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Grid,
  Button,
  Box,
  Alert,
  Snackbar,
  Divider,
} from '@mui/material';
import {
  Save as SaveIcon,
  NavigateNext as NextIcon,
  Description as ReportIcon,
} from '@mui/icons-material';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import dayjs from 'dayjs';

// Import form components
import FormTextField from '../common/FormTextField';
import FormDropdown from '../common/FormDropdown';
import FormDatePicker from '../common/FormDatePicker';
import FormRadioGroup from '../common/FormRadioGroup';
import LoadingSpinner from '../common/LoadingSpinner';

// Import services and utilities
import { createValidationSchema, areRequiredFieldsFilled, getFieldValidationRules } from '../../services/validation';
import { FIELD_CONFIG, DROPDOWN_OPTIONS } from '../../utils/constants';
import api from '../../services/api';

const PersonalDetailsForm = ({ clientId, onSave, onNext }) => {
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [dropdownData, setDropdownData] = useState(DROPDOWN_OPTIONS);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });

  // Initialize form with validation schema
  const {
    control,
    handleSubmit,
    watch,
    setValue,
    getValues,
    formState: { errors, isValid },
  } = useForm({
    resolver: yupResolver(createValidationSchema()),
    mode: 'onChange',
    defaultValues: {
      designation: '',
      title: '',
      initials: '',
      firstName: '',
      lastName: '',
      nameDenominatedByInitials: '',
      civilStatus: '',
      havingChildren: '',
      nationality: '',
      dateOfBirth: null,
      passportNo: '',
      rejoin: '',
      preferredLanguage: '',
      nicNo: '',
      takafulAgent: '',
    },
  });

  // Watch form values for real-time validation
  const formValues = watch();
  const canProceedNext = areRequiredFieldsFilled(formValues) && isValid;

  // Load dropdown data on component mount
  useEffect(() => {
    const loadDropdownData = async () => {
      try {
        setLoading(true);
        const response = await api.dropdown.getAll();
        setDropdownData(response.data);
      } catch (error) {
        console.error('Error loading dropdown data:', error);
        // Use default options if API fails
        setDropdownData(DROPDOWN_OPTIONS);
      } finally {
        setLoading(false);
      }
    };

    loadDropdownData();
  }, []);

  // Load existing client data if editing
  useEffect(() => {
    if (clientId) {
      const loadClientData = async () => {
        try {
          setLoading(true);
          const response = await api.client.getById(clientId);
          const clientData = response.data;

          // Set form values
          Object.keys(clientData).forEach(key => {
            if (key === 'dateOfBirth' && clientData[key]) {
              setValue(key, new Date(clientData[key]));
            } else {
              setValue(key, clientData[key] || '');
            }
          });
        } catch (error) {
          console.error('Error loading client data:', error);
          showSnackbar('Error loading client data', 'error');
        } finally {
          setLoading(false);
        }
      };

      loadClientData();
    }
  }, [clientId, setValue]);

  const showSnackbar = (message, severity = 'success') => {
    setSnackbar({ open: true, message, severity });
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  const handleSave = async (data) => {
    try {
      setSaving(true);

      if (clientId) {
        await api.client.update(clientId, data);
        showSnackbar('Personal details updated successfully');
      } else {
        const response = await api.client.create(data);
        showSnackbar('Personal details saved successfully');
        if (onSave) onSave(response.data);
      }
    } catch (error) {
      console.error('Error saving data:', error);
      showSnackbar('Error saving personal details', 'error');
    } finally {
      setSaving(false);
    }
  };

  const handleNext = async (data) => {
    try {
      setSaving(true);

      if (clientId) {
        await api.client.update(clientId, data);
      } else {
        const response = await api.client.create(data);
        if (onSave) onSave(response.data);
      }

      showSnackbar('Personal details saved successfully');
      if (onNext) onNext();
    } catch (error) {
      console.error('Error saving data:', error);
      showSnackbar('Error saving personal details', 'error');
    } finally {
      setSaving(false);
    }
  };

  const handleGenerateGSReport = async () => {
    if (!clientId) {
      showSnackbar('Please save the form first before generating reports', 'warning');
      return;
    }

    try {
      setLoading(true);
      const response = await api.report.generateGS(clientId);
      showSnackbar(response.data.message);
      // In a real app, you would download or open the PDF
      console.log('GS Report URL:', response.data.reportUrl);
    } catch (error) {
      showSnackbar(error.message, 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateHOBReport = async () => {
    if (!clientId) {
      showSnackbar('Please save the form first before generating reports', 'warning');
      return;
    }

    try {
      setLoading(true);
      const response = await api.report.generateHOB(clientId);
      showSnackbar(response.data.message);
      // In a real app, you would download or open the PDF
      console.log('HOB Report URL:', response.data.reportUrl);
    } catch (error) {
      showSnackbar(error.message, 'error');
    } finally {
      setLoading(false);
    }
  };

  if (loading && !clientId) {
    return <LoadingSpinner message="Loading form data..." />;
  }

  return (
    <Card elevation={3}>
      <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
        <Typography variant="h5" component="h2" gutterBottom sx={{ mb: 3 }}>
          Personal Details
        </Typography>

        <form onSubmit={handleSubmit(handleNext)}>
          <Grid container spacing={3}>
            {/* Row 1: Designation, Title, Initials */}
            <Grid item xs={12} sm={4}>
              <FormDropdown
                name="designation"
                control={control}
                label={FIELD_CONFIG.designation.label}
                required={FIELD_CONFIG.designation.required}
                options={dropdownData.designation || []}
                loading={loading}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <FormDropdown
                name="title"
                control={control}
                label={FIELD_CONFIG.title.label}
                required={FIELD_CONFIG.title.required}
                options={dropdownData.title || []}
                loading={loading}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <FormTextField
                name="initials"
                control={control}
                label={FIELD_CONFIG.initials.label}
                required={FIELD_CONFIG.initials.required}
                maxLength={FIELD_CONFIG.initials.maxLength}
              />
            </Grid>

            {/* Row 2: First Name, Last Name */}
            <Grid item xs={12} sm={6}>
              <FormTextField
                name="firstName"
                control={control}
                label={FIELD_CONFIG.firstName.label}
                required={FIELD_CONFIG.firstName.required}
                maxLength={FIELD_CONFIG.firstName.maxLength}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormTextField
                name="lastName"
                control={control}
                label={FIELD_CONFIG.lastName.label}
                required={FIELD_CONFIG.lastName.required}
                maxLength={FIELD_CONFIG.lastName.maxLength}
              />
            </Grid>

            {/* Row 3: Name Denominated by Initials */}
            <Grid item xs={12}>
              <FormTextField
                name="nameDenominatedByInitials"
                control={control}
                label={FIELD_CONFIG.nameDenominatedByInitials.label}
                required={FIELD_CONFIG.nameDenominatedByInitials.required}
                maxLength={FIELD_CONFIG.nameDenominatedByInitials.maxLength}
                validationRules={getFieldValidationRules('nameDenominatedByInitials')}
              />
            </Grid>

            {/* Row 4: Civil Status, Having Children */}
            <Grid item xs={12} sm={6}>
              <FormDropdown
                name="civilStatus"
                control={control}
                label={FIELD_CONFIG.civilStatus.label}
                required={FIELD_CONFIG.civilStatus.required}
                options={dropdownData.civilStatus || []}
                loading={loading}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormRadioGroup
                name="havingChildren"
                control={control}
                label={FIELD_CONFIG.havingChildren.label}
                required={FIELD_CONFIG.havingChildren.required}
                options={dropdownData.havingChildren || []}
                row={true}
              />
            </Grid>

            {/* Row 5: Nationality, Date of Birth */}
            <Grid item xs={12} sm={6}>
              <FormDropdown
                name="nationality"
                control={control}
                label={FIELD_CONFIG.nationality.label}
                required={FIELD_CONFIG.nationality.required}
                options={dropdownData.nationality || []}
                loading={loading}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormDatePicker
                name="dateOfBirth"
                control={control}
                label={FIELD_CONFIG.dateOfBirth.label}
                required={FIELD_CONFIG.dateOfBirth.required}
                format={FIELD_CONFIG.dateOfBirth.format}
                maxDate={dayjs().subtract(18, 'year')}
                helperText="Minimum age: 18 years"
              />
            </Grid>

            {/* Row 6: Passport No, Rejoin */}
            <Grid item xs={12} sm={6}>
              <FormTextField
                name="passportNo"
                control={control}
                label={FIELD_CONFIG.passportNo.label}
                required={FIELD_CONFIG.passportNo.required}
                maxLength={FIELD_CONFIG.passportNo.maxLength}
                validationRules={getFieldValidationRules('passportNo')}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormTextField
                name="rejoin"
                control={control}
                label={FIELD_CONFIG.rejoin.label}
                required={FIELD_CONFIG.rejoin.required}
                validationRules={getFieldValidationRules('rejoin')}
              />
            </Grid>

            {/* Row 7: Preferred Language, NIC No */}
            <Grid item xs={12} sm={6}>
              <FormDropdown
                name="preferredLanguage"
                control={control}
                label={FIELD_CONFIG.preferredLanguage.label}
                required={FIELD_CONFIG.preferredLanguage.required}
                options={dropdownData.preferredLanguage || []}
                loading={loading}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormTextField
                name="nicNo"
                control={control}
                label={FIELD_CONFIG.nicNo.label}
                required={FIELD_CONFIG.nicNo.required}
                validationRules={getFieldValidationRules('nicNo')}
                helperText="Must be unique across all records"
              />
            </Grid>

            {/* Row 8: Takaful Agent */}
            <Grid item xs={12} sm={6}>
              <FormDropdown
                name="takafulAgent"
                control={control}
                label={FIELD_CONFIG.takafulAgent.label}
                required={FIELD_CONFIG.takafulAgent.required}
                options={dropdownData.takafulAgent || []}
              />
            </Grid>
          </Grid>

          <Divider sx={{ my: 3 }} />

          {/* Action Buttons */}
          <Box sx={{
            display: 'flex',
            flexDirection: { xs: 'column', sm: 'row' },
            gap: 2,
            justifyContent: 'space-between',
            alignItems: { xs: 'stretch', sm: 'center' }
          }}>
            {/* Left side buttons */}
            <Box sx={{
              display: 'flex',
              flexDirection: { xs: 'column', sm: 'row' },
              gap: 2
            }}>
              <Button
                variant="outlined"
                startIcon={<SaveIcon />}
                onClick={handleSubmit(handleSave)}
                disabled={saving || loading}
                sx={{ minWidth: { xs: 'auto', sm: 120 } }}
              >
                {saving ? 'Saving...' : 'Save'}
              </Button>

              <Button
                variant="contained"
                startIcon={<NextIcon />}
                onClick={handleSubmit(handleNext)}
                disabled={!canProceedNext || saving || loading}
                sx={{ minWidth: { xs: 'auto', sm: 120 } }}
              >
                {saving ? 'Saving...' : 'Next'}
              </Button>
            </Box>

            {/* Right side buttons - Reports */}
            <Box sx={{
              display: 'flex',
              flexDirection: { xs: 'column', sm: 'row' },
              gap: 2
            }}>
              <Button
                variant="outlined"
                startIcon={<ReportIcon />}
                onClick={handleGenerateGSReport}
                disabled={loading || !clientId}
                sx={{ minWidth: { xs: 'auto', sm: 140 } }}
              >
                GS Report
              </Button>

              <Button
                variant="outlined"
                startIcon={<ReportIcon />}
                onClick={handleGenerateHOBReport}
                disabled={loading || !clientId}
                sx={{ minWidth: { xs: 'auto', sm: 140 } }}
              >
                HOB Report
              </Button>
            </Box>
          </Box>
        </form>

        {/* Required Fields Info */}
        <Alert severity="info" sx={{ mt: 3 }}>
          <Typography variant="body2">
            <strong>Required fields:</strong> Designation, First Name, Last Name, Civil Status,
            Having Children, Nationality, Date of Birth, NIC No, and Takaful Agent must be completed
            before proceeding to the next step.
          </Typography>
        </Alert>

        {/* Snackbar for notifications */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        >
          <Alert
            onClose={handleCloseSnackbar}
            severity={snackbar.severity}
            sx={{ width: '100%' }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </CardContent>
    </Card>
  );
};

export default PersonalDetailsForm;
